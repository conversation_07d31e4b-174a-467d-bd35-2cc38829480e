"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@emotion+cache@11.14.0";
exports.ids = ["vendor-chunks/@emotion+cache@11.14.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@emotion+cache@11.14.0/node_modules/@emotion/cache/dist/emotion-cache.development.esm.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@emotion+cache@11.14.0/node_modules/@emotion/cache/dist/emotion-cache.development.esm.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ createCache)\n/* harmony export */ });\n/* harmony import */ var _emotion_sheet__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @emotion/sheet */ \"(ssr)/./node_modules/.pnpm/@emotion+sheet@1.4.0/node_modules/@emotion/sheet/dist/emotion-sheet.development.esm.js\");\n/* harmony import */ var stylis__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! stylis */ \"(ssr)/./node_modules/.pnpm/stylis@4.2.0/node_modules/stylis/src/Tokenizer.js\");\n/* harmony import */ var stylis__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! stylis */ \"(ssr)/./node_modules/.pnpm/stylis@4.2.0/node_modules/stylis/src/Utility.js\");\n/* harmony import */ var stylis__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! stylis */ \"(ssr)/./node_modules/.pnpm/stylis@4.2.0/node_modules/stylis/src/Enum.js\");\n/* harmony import */ var stylis__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! stylis */ \"(ssr)/./node_modules/.pnpm/stylis@4.2.0/node_modules/stylis/src/Serializer.js\");\n/* harmony import */ var stylis__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! stylis */ \"(ssr)/./node_modules/.pnpm/stylis@4.2.0/node_modules/stylis/src/Middleware.js\");\n/* harmony import */ var stylis__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! stylis */ \"(ssr)/./node_modules/.pnpm/stylis@4.2.0/node_modules/stylis/src/Parser.js\");\n/* harmony import */ var _emotion_weak_memoize__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @emotion/weak-memoize */ \"(ssr)/./node_modules/.pnpm/@emotion+weak-memoize@0.4.0/node_modules/@emotion/weak-memoize/dist/emotion-weak-memoize.esm.js\");\n/* harmony import */ var _emotion_memoize__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @emotion/memoize */ \"(ssr)/./node_modules/.pnpm/@emotion+memoize@0.9.0/node_modules/@emotion/memoize/dist/emotion-memoize.esm.js\");\n\n\n\n\n\nvar isBrowser = typeof document !== 'undefined';\n\nvar identifierWithPointTracking = function identifierWithPointTracking(begin, points, index) {\n  var previous = 0;\n  var character = 0;\n\n  while (true) {\n    previous = character;\n    character = (0,stylis__WEBPACK_IMPORTED_MODULE_3__.peek)(); // &\\f\n\n    if (previous === 38 && character === 12) {\n      points[index] = 1;\n    }\n\n    if ((0,stylis__WEBPACK_IMPORTED_MODULE_3__.token)(character)) {\n      break;\n    }\n\n    (0,stylis__WEBPACK_IMPORTED_MODULE_3__.next)();\n  }\n\n  return (0,stylis__WEBPACK_IMPORTED_MODULE_3__.slice)(begin, stylis__WEBPACK_IMPORTED_MODULE_3__.position);\n};\n\nvar toRules = function toRules(parsed, points) {\n  // pretend we've started with a comma\n  var index = -1;\n  var character = 44;\n\n  do {\n    switch ((0,stylis__WEBPACK_IMPORTED_MODULE_3__.token)(character)) {\n      case 0:\n        // &\\f\n        if (character === 38 && (0,stylis__WEBPACK_IMPORTED_MODULE_3__.peek)() === 12) {\n          // this is not 100% correct, we don't account for literal sequences here - like for example quoted strings\n          // stylis inserts \\f after & to know when & where it should replace this sequence with the context selector\n          // and when it should just concatenate the outer and inner selectors\n          // it's very unlikely for this sequence to actually appear in a different context, so we just leverage this fact here\n          points[index] = 1;\n        }\n\n        parsed[index] += identifierWithPointTracking(stylis__WEBPACK_IMPORTED_MODULE_3__.position - 1, points, index);\n        break;\n\n      case 2:\n        parsed[index] += (0,stylis__WEBPACK_IMPORTED_MODULE_3__.delimit)(character);\n        break;\n\n      case 4:\n        // comma\n        if (character === 44) {\n          // colon\n          parsed[++index] = (0,stylis__WEBPACK_IMPORTED_MODULE_3__.peek)() === 58 ? '&\\f' : '';\n          points[index] = parsed[index].length;\n          break;\n        }\n\n      // fallthrough\n\n      default:\n        parsed[index] += (0,stylis__WEBPACK_IMPORTED_MODULE_4__.from)(character);\n    }\n  } while (character = (0,stylis__WEBPACK_IMPORTED_MODULE_3__.next)());\n\n  return parsed;\n};\n\nvar getRules = function getRules(value, points) {\n  return (0,stylis__WEBPACK_IMPORTED_MODULE_3__.dealloc)(toRules((0,stylis__WEBPACK_IMPORTED_MODULE_3__.alloc)(value), points));\n}; // WeakSet would be more appropriate, but only WeakMap is supported in IE11\n\n\nvar fixedElements = /* #__PURE__ */new WeakMap();\nvar compat = function compat(element) {\n  if (element.type !== 'rule' || !element.parent || // positive .length indicates that this rule contains pseudo\n  // negative .length indicates that this rule has been already prefixed\n  element.length < 1) {\n    return;\n  }\n\n  var value = element.value;\n  var parent = element.parent;\n  var isImplicitRule = element.column === parent.column && element.line === parent.line;\n\n  while (parent.type !== 'rule') {\n    parent = parent.parent;\n    if (!parent) return;\n  } // short-circuit for the simplest case\n\n\n  if (element.props.length === 1 && value.charCodeAt(0) !== 58\n  /* colon */\n  && !fixedElements.get(parent)) {\n    return;\n  } // if this is an implicitly inserted rule (the one eagerly inserted at the each new nested level)\n  // then the props has already been manipulated beforehand as they that array is shared between it and its \"rule parent\"\n\n\n  if (isImplicitRule) {\n    return;\n  }\n\n  fixedElements.set(element, true);\n  var points = [];\n  var rules = getRules(value, points);\n  var parentRules = parent.props;\n\n  for (var i = 0, k = 0; i < rules.length; i++) {\n    for (var j = 0; j < parentRules.length; j++, k++) {\n      element.props[k] = points[i] ? rules[i].replace(/&\\f/g, parentRules[j]) : parentRules[j] + \" \" + rules[i];\n    }\n  }\n};\nvar removeLabel = function removeLabel(element) {\n  if (element.type === 'decl') {\n    var value = element.value;\n\n    if ( // charcode for l\n    value.charCodeAt(0) === 108 && // charcode for b\n    value.charCodeAt(2) === 98) {\n      // this ignores label\n      element[\"return\"] = '';\n      element.value = '';\n    }\n  }\n};\nvar ignoreFlag = 'emotion-disable-server-rendering-unsafe-selector-warning-please-do-not-use-this-the-warning-exists-for-a-reason';\n\nvar isIgnoringComment = function isIgnoringComment(element) {\n  return element.type === 'comm' && element.children.indexOf(ignoreFlag) > -1;\n};\n\nvar createUnsafeSelectorsAlarm = function createUnsafeSelectorsAlarm(cache) {\n  return function (element, index, children) {\n    if (element.type !== 'rule' || cache.compat) return;\n    var unsafePseudoClasses = element.value.match(/(:first|:nth|:nth-last)-child/g);\n\n    if (unsafePseudoClasses) {\n      var isNested = !!element.parent; // in nested rules comments become children of the \"auto-inserted\" rule and that's always the `element.parent`\n      //\n      // considering this input:\n      // .a {\n      //   .b /* comm */ {}\n      //   color: hotpink;\n      // }\n      // we get output corresponding to this:\n      // .a {\n      //   & {\n      //     /* comm */\n      //     color: hotpink;\n      //   }\n      //   .b {}\n      // }\n\n      var commentContainer = isNested ? element.parent.children : // global rule at the root level\n      children;\n\n      for (var i = commentContainer.length - 1; i >= 0; i--) {\n        var node = commentContainer[i];\n\n        if (node.line < element.line) {\n          break;\n        } // it is quite weird but comments are *usually* put at `column: element.column - 1`\n        // so we seek *from the end* for the node that is earlier than the rule's `element` and check that\n        // this will also match inputs like this:\n        // .a {\n        //   /* comm */\n        //   .b {}\n        // }\n        //\n        // but that is fine\n        //\n        // it would be the easiest to change the placement of the comment to be the first child of the rule:\n        // .a {\n        //   .b { /* comm */ }\n        // }\n        // with such inputs we wouldn't have to search for the comment at all\n        // TODO: consider changing this comment placement in the next major version\n\n\n        if (node.column < element.column) {\n          if (isIgnoringComment(node)) {\n            return;\n          }\n\n          break;\n        }\n      }\n\n      unsafePseudoClasses.forEach(function (unsafePseudoClass) {\n        console.error(\"The pseudo class \\\"\" + unsafePseudoClass + \"\\\" is potentially unsafe when doing server-side rendering. Try changing it to \\\"\" + unsafePseudoClass.split('-child')[0] + \"-of-type\\\".\");\n      });\n    }\n  };\n};\n\nvar isImportRule = function isImportRule(element) {\n  return element.type.charCodeAt(1) === 105 && element.type.charCodeAt(0) === 64;\n};\n\nvar isPrependedWithRegularRules = function isPrependedWithRegularRules(index, children) {\n  for (var i = index - 1; i >= 0; i--) {\n    if (!isImportRule(children[i])) {\n      return true;\n    }\n  }\n\n  return false;\n}; // use this to remove incorrect elements from further processing\n// so they don't get handed to the `sheet` (or anything else)\n// as that could potentially lead to additional logs which in turn could be overhelming to the user\n\n\nvar nullifyElement = function nullifyElement(element) {\n  element.type = '';\n  element.value = '';\n  element[\"return\"] = '';\n  element.children = '';\n  element.props = '';\n};\n\nvar incorrectImportAlarm = function incorrectImportAlarm(element, index, children) {\n  if (!isImportRule(element)) {\n    return;\n  }\n\n  if (element.parent) {\n    console.error(\"`@import` rules can't be nested inside other rules. Please move it to the top level and put it before regular rules. Keep in mind that they can only be used within global styles.\");\n    nullifyElement(element);\n  } else if (isPrependedWithRegularRules(index, children)) {\n    console.error(\"`@import` rules can't be after other rules. Please put your `@import` rules before your other rules.\");\n    nullifyElement(element);\n  }\n};\n\n/* eslint-disable no-fallthrough */\n\nfunction prefix(value, length) {\n  switch ((0,stylis__WEBPACK_IMPORTED_MODULE_4__.hash)(value, length)) {\n    // color-adjust\n    case 5103:\n      return stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT + 'print-' + value + value;\n    // animation, animation-(delay|direction|duration|fill-mode|iteration-count|name|play-state|timing-function)\n\n    case 5737:\n    case 4201:\n    case 3177:\n    case 3433:\n    case 1641:\n    case 4457:\n    case 2921: // text-decoration, filter, clip-path, backface-visibility, column, box-decoration-break\n\n    case 5572:\n    case 6356:\n    case 5844:\n    case 3191:\n    case 6645:\n    case 3005: // mask, mask-image, mask-(mode|clip|size), mask-(repeat|origin), mask-position, mask-composite,\n\n    case 6391:\n    case 5879:\n    case 5623:\n    case 6135:\n    case 4599:\n    case 4855: // background-clip, columns, column-(count|fill|gap|rule|rule-color|rule-style|rule-width|span|width)\n\n    case 4215:\n    case 6389:\n    case 5109:\n    case 5365:\n    case 5621:\n    case 3829:\n      return stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT + value + value;\n    // appearance, user-select, transform, hyphens, text-size-adjust\n\n    case 5349:\n    case 4246:\n    case 4810:\n    case 6968:\n    case 2756:\n      return stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT + value + stylis__WEBPACK_IMPORTED_MODULE_5__.MOZ + value + stylis__WEBPACK_IMPORTED_MODULE_5__.MS + value + value;\n    // flex, flex-direction\n\n    case 6828:\n    case 4268:\n      return stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT + value + stylis__WEBPACK_IMPORTED_MODULE_5__.MS + value + value;\n    // order\n\n    case 6165:\n      return stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT + value + stylis__WEBPACK_IMPORTED_MODULE_5__.MS + 'flex-' + value + value;\n    // align-items\n\n    case 5187:\n      return stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT + value + (0,stylis__WEBPACK_IMPORTED_MODULE_4__.replace)(value, /(\\w+).+(:[^]+)/, stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT + 'box-$1$2' + stylis__WEBPACK_IMPORTED_MODULE_5__.MS + 'flex-$1$2') + value;\n    // align-self\n\n    case 5443:\n      return stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT + value + stylis__WEBPACK_IMPORTED_MODULE_5__.MS + 'flex-item-' + (0,stylis__WEBPACK_IMPORTED_MODULE_4__.replace)(value, /flex-|-self/, '') + value;\n    // align-content\n\n    case 4675:\n      return stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT + value + stylis__WEBPACK_IMPORTED_MODULE_5__.MS + 'flex-line-pack' + (0,stylis__WEBPACK_IMPORTED_MODULE_4__.replace)(value, /align-content|flex-|-self/, '') + value;\n    // flex-shrink\n\n    case 5548:\n      return stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT + value + stylis__WEBPACK_IMPORTED_MODULE_5__.MS + (0,stylis__WEBPACK_IMPORTED_MODULE_4__.replace)(value, 'shrink', 'negative') + value;\n    // flex-basis\n\n    case 5292:\n      return stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT + value + stylis__WEBPACK_IMPORTED_MODULE_5__.MS + (0,stylis__WEBPACK_IMPORTED_MODULE_4__.replace)(value, 'basis', 'preferred-size') + value;\n    // flex-grow\n\n    case 6060:\n      return stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT + 'box-' + (0,stylis__WEBPACK_IMPORTED_MODULE_4__.replace)(value, '-grow', '') + stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT + value + stylis__WEBPACK_IMPORTED_MODULE_5__.MS + (0,stylis__WEBPACK_IMPORTED_MODULE_4__.replace)(value, 'grow', 'positive') + value;\n    // transition\n\n    case 4554:\n      return stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT + (0,stylis__WEBPACK_IMPORTED_MODULE_4__.replace)(value, /([^-])(transform)/g, '$1' + stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT + '$2') + value;\n    // cursor\n\n    case 6187:\n      return (0,stylis__WEBPACK_IMPORTED_MODULE_4__.replace)((0,stylis__WEBPACK_IMPORTED_MODULE_4__.replace)((0,stylis__WEBPACK_IMPORTED_MODULE_4__.replace)(value, /(zoom-|grab)/, stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT + '$1'), /(image-set)/, stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT + '$1'), value, '') + value;\n    // background, background-image\n\n    case 5495:\n    case 3959:\n      return (0,stylis__WEBPACK_IMPORTED_MODULE_4__.replace)(value, /(image-set\\([^]*)/, stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT + '$1' + '$`$1');\n    // justify-content\n\n    case 4968:\n      return (0,stylis__WEBPACK_IMPORTED_MODULE_4__.replace)((0,stylis__WEBPACK_IMPORTED_MODULE_4__.replace)(value, /(.+:)(flex-)?(.*)/, stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT + 'box-pack:$3' + stylis__WEBPACK_IMPORTED_MODULE_5__.MS + 'flex-pack:$3'), /s.+-b[^;]+/, 'justify') + stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT + value + value;\n    // (margin|padding)-inline-(start|end)\n\n    case 4095:\n    case 3583:\n    case 4068:\n    case 2532:\n      return (0,stylis__WEBPACK_IMPORTED_MODULE_4__.replace)(value, /(.+)-inline(.+)/, stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT + '$1$2') + value;\n    // (min|max)?(width|height|inline-size|block-size)\n\n    case 8116:\n    case 7059:\n    case 5753:\n    case 5535:\n    case 5445:\n    case 5701:\n    case 4933:\n    case 4677:\n    case 5533:\n    case 5789:\n    case 5021:\n    case 4765:\n      // stretch, max-content, min-content, fill-available\n      if ((0,stylis__WEBPACK_IMPORTED_MODULE_4__.strlen)(value) - 1 - length > 6) switch ((0,stylis__WEBPACK_IMPORTED_MODULE_4__.charat)(value, length + 1)) {\n        // (m)ax-content, (m)in-content\n        case 109:\n          // -\n          if ((0,stylis__WEBPACK_IMPORTED_MODULE_4__.charat)(value, length + 4) !== 45) break;\n        // (f)ill-available, (f)it-content\n\n        case 102:\n          return (0,stylis__WEBPACK_IMPORTED_MODULE_4__.replace)(value, /(.+:)(.+)-([^]+)/, '$1' + stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT + '$2-$3' + '$1' + stylis__WEBPACK_IMPORTED_MODULE_5__.MOZ + ((0,stylis__WEBPACK_IMPORTED_MODULE_4__.charat)(value, length + 3) == 108 ? '$3' : '$2-$3')) + value;\n        // (s)tretch\n\n        case 115:\n          return ~(0,stylis__WEBPACK_IMPORTED_MODULE_4__.indexof)(value, 'stretch') ? prefix((0,stylis__WEBPACK_IMPORTED_MODULE_4__.replace)(value, 'stretch', 'fill-available'), length) + value : value;\n      }\n      break;\n    // position: sticky\n\n    case 4949:\n      // (s)ticky?\n      if ((0,stylis__WEBPACK_IMPORTED_MODULE_4__.charat)(value, length + 1) !== 115) break;\n    // display: (flex|inline-flex)\n\n    case 6444:\n      switch ((0,stylis__WEBPACK_IMPORTED_MODULE_4__.charat)(value, (0,stylis__WEBPACK_IMPORTED_MODULE_4__.strlen)(value) - 3 - (~(0,stylis__WEBPACK_IMPORTED_MODULE_4__.indexof)(value, '!important') && 10))) {\n        // stic(k)y\n        case 107:\n          return (0,stylis__WEBPACK_IMPORTED_MODULE_4__.replace)(value, ':', ':' + stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT) + value;\n        // (inline-)?fl(e)x\n\n        case 101:\n          return (0,stylis__WEBPACK_IMPORTED_MODULE_4__.replace)(value, /(.+:)([^;!]+)(;|!.+)?/, '$1' + stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT + ((0,stylis__WEBPACK_IMPORTED_MODULE_4__.charat)(value, 14) === 45 ? 'inline-' : '') + 'box$3' + '$1' + stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT + '$2$3' + '$1' + stylis__WEBPACK_IMPORTED_MODULE_5__.MS + '$2box$3') + value;\n      }\n\n      break;\n    // writing-mode\n\n    case 5936:\n      switch ((0,stylis__WEBPACK_IMPORTED_MODULE_4__.charat)(value, length + 11)) {\n        // vertical-l(r)\n        case 114:\n          return stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT + value + stylis__WEBPACK_IMPORTED_MODULE_5__.MS + (0,stylis__WEBPACK_IMPORTED_MODULE_4__.replace)(value, /[svh]\\w+-[tblr]{2}/, 'tb') + value;\n        // vertical-r(l)\n\n        case 108:\n          return stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT + value + stylis__WEBPACK_IMPORTED_MODULE_5__.MS + (0,stylis__WEBPACK_IMPORTED_MODULE_4__.replace)(value, /[svh]\\w+-[tblr]{2}/, 'tb-rl') + value;\n        // horizontal(-)tb\n\n        case 45:\n          return stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT + value + stylis__WEBPACK_IMPORTED_MODULE_5__.MS + (0,stylis__WEBPACK_IMPORTED_MODULE_4__.replace)(value, /[svh]\\w+-[tblr]{2}/, 'lr') + value;\n      }\n\n      return stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT + value + stylis__WEBPACK_IMPORTED_MODULE_5__.MS + value + value;\n  }\n\n  return value;\n}\n\nvar prefixer = function prefixer(element, index, children, callback) {\n  if (element.length > -1) if (!element[\"return\"]) switch (element.type) {\n    case stylis__WEBPACK_IMPORTED_MODULE_5__.DECLARATION:\n      element[\"return\"] = prefix(element.value, element.length);\n      break;\n\n    case stylis__WEBPACK_IMPORTED_MODULE_5__.KEYFRAMES:\n      return (0,stylis__WEBPACK_IMPORTED_MODULE_6__.serialize)([(0,stylis__WEBPACK_IMPORTED_MODULE_3__.copy)(element, {\n        value: (0,stylis__WEBPACK_IMPORTED_MODULE_4__.replace)(element.value, '@', '@' + stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT)\n      })], callback);\n\n    case stylis__WEBPACK_IMPORTED_MODULE_5__.RULESET:\n      if (element.length) return (0,stylis__WEBPACK_IMPORTED_MODULE_4__.combine)(element.props, function (value) {\n        switch ((0,stylis__WEBPACK_IMPORTED_MODULE_4__.match)(value, /(::plac\\w+|:read-\\w+)/)) {\n          // :read-(only|write)\n          case ':read-only':\n          case ':read-write':\n            return (0,stylis__WEBPACK_IMPORTED_MODULE_6__.serialize)([(0,stylis__WEBPACK_IMPORTED_MODULE_3__.copy)(element, {\n              props: [(0,stylis__WEBPACK_IMPORTED_MODULE_4__.replace)(value, /:(read-\\w+)/, ':' + stylis__WEBPACK_IMPORTED_MODULE_5__.MOZ + '$1')]\n            })], callback);\n          // :placeholder\n\n          case '::placeholder':\n            return (0,stylis__WEBPACK_IMPORTED_MODULE_6__.serialize)([(0,stylis__WEBPACK_IMPORTED_MODULE_3__.copy)(element, {\n              props: [(0,stylis__WEBPACK_IMPORTED_MODULE_4__.replace)(value, /:(plac\\w+)/, ':' + stylis__WEBPACK_IMPORTED_MODULE_5__.WEBKIT + 'input-$1')]\n            }), (0,stylis__WEBPACK_IMPORTED_MODULE_3__.copy)(element, {\n              props: [(0,stylis__WEBPACK_IMPORTED_MODULE_4__.replace)(value, /:(plac\\w+)/, ':' + stylis__WEBPACK_IMPORTED_MODULE_5__.MOZ + '$1')]\n            }), (0,stylis__WEBPACK_IMPORTED_MODULE_3__.copy)(element, {\n              props: [(0,stylis__WEBPACK_IMPORTED_MODULE_4__.replace)(value, /:(plac\\w+)/, stylis__WEBPACK_IMPORTED_MODULE_5__.MS + 'input-$1')]\n            })], callback);\n        }\n\n        return '';\n      });\n  }\n};\n\nvar getServerStylisCache = isBrowser ? undefined : (0,_emotion_weak_memoize__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(function () {\n  return (0,_emotion_memoize__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(function () {\n    return {};\n  });\n});\nvar defaultStylisPlugins = [prefixer];\nvar getSourceMap;\n\n{\n  var sourceMapPattern = /\\/\\*#\\ssourceMappingURL=data:application\\/json;\\S+\\s+\\*\\//g;\n\n  getSourceMap = function getSourceMap(styles) {\n    var matches = styles.match(sourceMapPattern);\n    if (!matches) return;\n    return matches[matches.length - 1];\n  };\n}\n\nvar createCache = function createCache(options) {\n  var key = options.key;\n\n  if (!key) {\n    throw new Error(\"You have to configure `key` for your cache. Please make sure it's unique (and not equal to 'css') as it's used for linking styles to your cache.\\n\" + \"If multiple caches share the same key they might \\\"fight\\\" for each other's style elements.\");\n  }\n\n  if (isBrowser && key === 'css') {\n    var ssrStyles = document.querySelectorAll(\"style[data-emotion]:not([data-s])\"); // get SSRed styles out of the way of React's hydration\n    // document.head is a safe place to move them to(though note document.head is not necessarily the last place they will be)\n    // note this very very intentionally targets all style elements regardless of the key to ensure\n    // that creating a cache works inside of render of a React component\n\n    Array.prototype.forEach.call(ssrStyles, function (node) {\n      // we want to only move elements which have a space in the data-emotion attribute value\n      // because that indicates that it is an Emotion 11 server-side rendered style elements\n      // while we will already ignore Emotion 11 client-side inserted styles because of the :not([data-s]) part in the selector\n      // Emotion 10 client-side inserted styles did not have data-s (but importantly did not have a space in their data-emotion attributes)\n      // so checking for the space ensures that loading Emotion 11 after Emotion 10 has inserted some styles\n      // will not result in the Emotion 10 styles being destroyed\n      var dataEmotionAttribute = node.getAttribute('data-emotion');\n\n      if (dataEmotionAttribute.indexOf(' ') === -1) {\n        return;\n      }\n\n      document.head.appendChild(node);\n      node.setAttribute('data-s', '');\n    });\n  }\n\n  var stylisPlugins = options.stylisPlugins || defaultStylisPlugins;\n\n  {\n    if (/[^a-z-]/.test(key)) {\n      throw new Error(\"Emotion key must only contain lower case alphabetical characters and - but \\\"\" + key + \"\\\" was passed\");\n    }\n  }\n\n  var inserted = {};\n  var container;\n  var nodesToHydrate = [];\n\n  if (isBrowser) {\n    container = options.container || document.head;\n    Array.prototype.forEach.call( // this means we will ignore elements which don't have a space in them which\n    // means that the style elements we're looking at are only Emotion 11 server-rendered style elements\n    document.querySelectorAll(\"style[data-emotion^=\\\"\" + key + \" \\\"]\"), function (node) {\n      var attrib = node.getAttribute(\"data-emotion\").split(' ');\n\n      for (var i = 1; i < attrib.length; i++) {\n        inserted[attrib[i]] = true;\n      }\n\n      nodesToHydrate.push(node);\n    });\n  }\n\n  var _insert;\n\n  var omnipresentPlugins = [compat, removeLabel];\n\n  {\n    omnipresentPlugins.push(createUnsafeSelectorsAlarm({\n      get compat() {\n        return cache.compat;\n      }\n\n    }), incorrectImportAlarm);\n  }\n\n  if (!getServerStylisCache) {\n    var currentSheet;\n    var finalizingPlugins = [stylis__WEBPACK_IMPORTED_MODULE_6__.stringify, function (element) {\n      if (!element.root) {\n        if (element[\"return\"]) {\n          currentSheet.insert(element[\"return\"]);\n        } else if (element.value && element.type !== stylis__WEBPACK_IMPORTED_MODULE_5__.COMMENT) {\n          // insert empty rule in non-production environments\n          // so @emotion/jest can grab `key` from the (JS)DOM for caches without any rules inserted yet\n          currentSheet.insert(element.value + \"{}\");\n        }\n      }\n    } ];\n    var serializer = (0,stylis__WEBPACK_IMPORTED_MODULE_7__.middleware)(omnipresentPlugins.concat(stylisPlugins, finalizingPlugins));\n\n    var stylis = function stylis(styles) {\n      return (0,stylis__WEBPACK_IMPORTED_MODULE_6__.serialize)((0,stylis__WEBPACK_IMPORTED_MODULE_8__.compile)(styles), serializer);\n    };\n\n    _insert = function insert(selector, serialized, sheet, shouldCache) {\n      currentSheet = sheet;\n\n      if (getSourceMap) {\n        var sourceMap = getSourceMap(serialized.styles);\n\n        if (sourceMap) {\n          currentSheet = {\n            insert: function insert(rule) {\n              sheet.insert(rule + sourceMap);\n            }\n          };\n        }\n      }\n\n      stylis(selector ? selector + \"{\" + serialized.styles + \"}\" : serialized.styles);\n\n      if (shouldCache) {\n        cache.inserted[serialized.name] = true;\n      }\n    };\n  } else {\n    var _finalizingPlugins = [stylis__WEBPACK_IMPORTED_MODULE_6__.stringify];\n\n    var _serializer = (0,stylis__WEBPACK_IMPORTED_MODULE_7__.middleware)(omnipresentPlugins.concat(stylisPlugins, _finalizingPlugins));\n\n    var _stylis = function _stylis(styles) {\n      return (0,stylis__WEBPACK_IMPORTED_MODULE_6__.serialize)((0,stylis__WEBPACK_IMPORTED_MODULE_8__.compile)(styles), _serializer);\n    };\n\n    var serverStylisCache = getServerStylisCache(stylisPlugins)(key);\n\n    var getRules = function getRules(selector, serialized) {\n      var name = serialized.name;\n\n      if (serverStylisCache[name] === undefined) {\n        serverStylisCache[name] = _stylis(selector ? selector + \"{\" + serialized.styles + \"}\" : serialized.styles);\n      }\n\n      return serverStylisCache[name];\n    };\n\n    _insert = function _insert(selector, serialized, sheet, shouldCache) {\n      var name = serialized.name;\n      var rules = getRules(selector, serialized);\n\n      if (cache.compat === undefined) {\n        // in regular mode, we don't set the styles on the inserted cache\n        // since we don't need to and that would be wasting memory\n        // we return them so that they are rendered in a style tag\n        if (shouldCache) {\n          cache.inserted[name] = true;\n        }\n\n        if (getSourceMap) {\n          var sourceMap = getSourceMap(serialized.styles);\n\n          if (sourceMap) {\n            return rules + sourceMap;\n          }\n        }\n\n        return rules;\n      } else {\n        // in compat mode, we put the styles on the inserted cache so\n        // that emotion-server can pull out the styles\n        // except when we don't want to cache it which was in Global but now\n        // is nowhere but we don't want to do a major right now\n        // and just in case we're going to leave the case here\n        // it's also not affecting client side bundle size\n        // so it's really not a big deal\n        if (shouldCache) {\n          cache.inserted[name] = rules;\n        } else {\n          return rules;\n        }\n      }\n    };\n  }\n\n  var cache = {\n    key: key,\n    sheet: new _emotion_sheet__WEBPACK_IMPORTED_MODULE_0__.StyleSheet({\n      key: key,\n      container: container,\n      nonce: options.nonce,\n      speedy: options.speedy,\n      prepend: options.prepend,\n      insertionPoint: options.insertionPoint\n    }),\n    nonce: options.nonce,\n    inserted: inserted,\n    registered: {},\n    insert: _insert\n  };\n  cache.sheet.hydrate(nodesToHydrate);\n  return cache;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@emotion+cache@11.14.0/node_modules/@emotion/cache/dist/emotion-cache.development.esm.js\n");

/***/ })

};
;