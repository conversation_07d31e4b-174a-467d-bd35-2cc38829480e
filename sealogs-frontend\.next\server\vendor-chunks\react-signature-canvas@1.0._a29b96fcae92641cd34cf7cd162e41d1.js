/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-signature-canvas@1.0._a29b96fcae92641cd34cf7cd162e41d1";
exports.ids = ["vendor-chunks/react-signature-canvas@1.0._a29b96fcae92641cd34cf7cd162e41d1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/react-signature-canvas@1.0._a29b96fcae92641cd34cf7cd162e41d1/node_modules/react-signature-canvas/build/index.js":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-signature-canvas@1.0._a29b96fcae92641cd34cf7cd162e41d1/node_modules/react-signature-canvas/build/index.js ***!
  \********************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("!function(e,t){ true?module.exports=t(__webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/.pnpm/prop-types@15.8.1/node_modules/prop-types/index.js\"),__webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"),__webpack_require__(/*! signature_pad */ \"(ssr)/./node_modules/.pnpm/signature_pad@2.3.2/node_modules/signature_pad/dist/signature_pad.mjs\"),__webpack_require__(/*! trim-canvas */ \"(ssr)/./node_modules/.pnpm/trim-canvas@0.1.2/node_modules/trim-canvas/build/index.js\")):0}(this,function(e,t,n,r){return function(e){function t(r){if(n[r])return n[r].exports;var a=n[r]={exports:{},id:r,loaded:!1};return e[r].call(a.exports,a,a.exports,t),a.loaded=!0,a.exports}var n={};return t.m=e,t.c=n,t.p=\"\",t(0)}([function(e,t,n){\"use strict\";function r(e){return e&&e.__esModule?e:{default:e}}function a(e,t){var n={};for(var r in e)t.indexOf(r)>=0||Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);return n}function o(e,t){if(!(e instanceof t))throw new TypeError(\"Cannot call a class as a function\")}function i(e,t){if(!e)throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");return!t||\"object\"!=typeof t&&\"function\"!=typeof t?e:t}function u(e,t){if(\"function\"!=typeof t&&null!==t)throw new TypeError(\"Super expression must either be null or a function, not \"+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,\"__esModule\",{value:!0});var s=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},c=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,\"value\"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),f=n(1),p=r(f),l=n(2),d=r(l),v=n(3),h=r(v),_=n(4),g=r(_),m=function(e){function t(){var e,n,r,u;o(this,t);for(var s=arguments.length,c=Array(s),f=0;f<s;f++)c[f]=arguments[f];return n=r=i(this,(e=t.__proto__||Object.getPrototypeOf(t)).call.apply(e,[this].concat(c))),r._sigPad=null,r._excludeOurProps=function(){var e=r.props,t=(e.canvasProps,e.clearOnResize,a(e,[\"canvasProps\",\"clearOnResize\"]));return t},r.getCanvas=function(){return r._canvas},r.getTrimmedCanvas=function(){var e=document.createElement(\"canvas\");return e.width=r._canvas.width,e.height=r._canvas.height,e.getContext(\"2d\").drawImage(r._canvas,0,0),(0,g.default)(e)},r.getSignaturePad=function(){return r._sigPad},r._checkClearOnResize=function(){r.props.clearOnResize&&r._resizeCanvas()},r._resizeCanvas=function(){var e=r.props.canvasProps||{},t=e.width,n=e.height;if(!t||!n){var a=r._canvas,o=Math.max(window.devicePixelRatio||1,1);t||(a.width=a.offsetWidth*o),n||(a.height=a.offsetHeight*o),a.getContext(\"2d\").scale(o,o),r.clear()}},r.on=function(){return window.addEventListener(\"resize\",r._checkClearOnResize),r._sigPad.on()},r.off=function(){return window.removeEventListener(\"resize\",r._checkClearOnResize),r._sigPad.off()},r.clear=function(){return r._sigPad.clear()},r.isEmpty=function(){return r._sigPad.isEmpty()},r.fromDataURL=function(e,t){return r._sigPad.fromDataURL(e,t)},r.toDataURL=function(e,t){return r._sigPad.toDataURL(e,t)},r.fromData=function(e){return r._sigPad.fromData(e)},r.toData=function(){return r._sigPad.toData()},u=n,i(r,u)}return u(t,e),c(t,[{key:\"componentDidMount\",value:function(){this._sigPad=new h.default(this._canvas,this._excludeOurProps()),this._resizeCanvas(),this.on()}},{key:\"componentWillUnmount\",value:function(){this.off()}},{key:\"componentDidUpdate\",value:function(){Object.assign(this._sigPad,this._excludeOurProps())}},{key:\"render\",value:function(){var e=this,t=this.props.canvasProps;return d.default.createElement(\"canvas\",s({ref:function(t){e._canvas=t}},t))}}]),t}(l.Component);m.propTypes={velocityFilterWeight:p.default.number,minWidth:p.default.number,maxWidth:p.default.number,minDistance:p.default.number,dotSize:p.default.oneOfType([p.default.number,p.default.func]),penColor:p.default.string,throttle:p.default.number,onEnd:p.default.func,onBegin:p.default.func,canvasProps:p.default.object,clearOnResize:p.default.bool},m.defaultProps={clearOnResize:!0},t.default=m},function(t,n){t.exports=e},function(e,n){e.exports=t},function(e,t){e.exports=n},function(e,t){e.exports=r}])});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-signature-canvas@1.0._a29b96fcae92641cd34cf7cd162e41d1/node_modules/react-signature-canvas/build/index.js\n");

/***/ })

};
;