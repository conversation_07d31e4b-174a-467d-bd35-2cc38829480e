"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/events.tsx":
/*!***************************************!*\
  !*** ./src/app/ui/logbook/events.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Events; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _forms_vessel_rescue__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./forms/vessel-rescue */ \"(app-pages-browser)/./src/app/ui/logbook/forms/vessel-rescue.tsx\");\n/* harmony import */ var _forms_person_rescue__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./forms/person-rescue */ \"(app-pages-browser)/./src/app/ui/logbook/forms/person-rescue.tsx\");\n/* harmony import */ var _forms_restricted_visibility__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./forms/restricted-visibility */ \"(app-pages-browser)/./src/app/ui/logbook/forms/restricted-visibility.tsx\");\n/* harmony import */ var _forms_bar_crossing__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./forms/bar-crossing */ \"(app-pages-browser)/./src/app/ui/logbook/forms/bar-crossing.tsx\");\n/* harmony import */ var _forms_passenger_drop_facility__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./forms/passenger-drop-facility */ \"(app-pages-browser)/./src/app/ui/logbook/forms/passenger-drop-facility.tsx\");\n/* harmony import */ var _forms_tasking__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./forms/tasking */ \"(app-pages-browser)/./src/app/ui/logbook/forms/tasking.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _forms_crew_training_event__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./forms/crew-training-event */ \"(app-pages-browser)/./src/app/ui/logbook/forms/crew-training-event.tsx\");\n/* harmony import */ var _forms_supernumerary_event__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./forms/supernumerary-event */ \"(app-pages-browser)/./src/app/ui/logbook/forms/supernumerary-event.tsx\");\n/* harmony import */ var _forms_passenger_vehicle_pick_drop_passenger_vehicle_pick_drop__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./forms/passenger-vehicle-pick-drop/passenger-vehicle-pick-drop */ \"(app-pages-browser)/./src/app/ui/logbook/forms/passenger-vehicle-pick-drop/passenger-vehicle-pick-drop.tsx\");\n/* harmony import */ var lodash_lowerCase__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! lodash/lowerCase */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/lowerCase.js\");\n/* harmony import */ var lodash_lowerCase__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(lodash_lowerCase__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var lodash_uniqueId__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! lodash/uniqueId */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/uniqueId.js\");\n/* harmony import */ var lodash_uniqueId__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var lodash_upperFirst__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! lodash/upperFirst */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/upperFirst.js\");\n/* harmony import */ var lodash_upperFirst__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(lodash_upperFirst__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var _forms_refuelling_bunkering__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./forms/refuelling-bunkering */ \"(app-pages-browser)/./src/app/ui/logbook/forms/refuelling-bunkering.tsx\");\n/* harmony import */ var _app_lib_vesselTypes__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/lib/vesselTypes */ \"(app-pages-browser)/./src/app/lib/vesselTypes.tsx\");\n/* harmony import */ var _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/app/lib/logbook-configuration */ \"(app-pages-browser)/./src/app/lib/logbook-configuration/index.ts\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _forms_InfringementNotices__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./forms/InfringementNotices */ \"(app-pages-browser)/./src/app/ui/logbook/forms/InfringementNotices.tsx\");\n/* harmony import */ var _forms_trip_update__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./forms/trip-update */ \"(app-pages-browser)/./src/app/ui/logbook/forms/trip-update.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_accordion__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/components/ui/accordion */ \"(app-pages-browser)/./src/components/ui/accordion.tsx\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _radio_logs_schedule__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./radio-logs-schedule */ \"(app-pages-browser)/./src/app/ui/logbook/radio-logs-schedule.tsx\");\n/* harmony import */ var _incident_record_incident_record_form__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ../incident-record/incident-record-form */ \"(app-pages-browser)/./src/app/ui/incident-record/incident-record-form.tsx\");\n/* harmony import */ var _forms_pilot_transfer__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ./forms/pilot-transfer */ \"(app-pages-browser)/./src/app/ui/logbook/forms/pilot-transfer.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* harmony import */ var _utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! ../../../../utils/responsiveLabel */ \"(app-pages-browser)/./utils/responsiveLabel.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Events(param) {\n    let { currentTrip, logBookConfig, updateTripReport, locked, geoLocations, tripReport, crewMembers, masterID, vessel, vessels, offline = false, setSelectedRow, setCurrentEventType, setCurrentStop, currentEventType, currentStop, tripReport_Stops, setTripReport_Stops, displayDangerousGoodsPvpd = false, displayDangerousGoodsPvpdSailing, setDisplayDangerousGoodsPvpd, setDisplayDangerousGoodsPvpdSailing, allPVPDDangerousGoods, setAllPVPDDangerousGoods, selectedDGRPVPD, setSelectedDGRPVPD, fuelLogs, logBookStartDate } = param;\n    var _currentTrip_tripReport_Stops, _currentTrip_tripEvents_nodes, _currentTrip_tripEvents, _currentTrip_tripReport_Stops_nodes, _currentTrip_tripReport_Stops1, _currentTrip_tripEvents_nodes1, _currentTrip_tripEvents1, _currentTrip_tripReport_Stops_nodes1, _currentTrip_tripReport_Stops2, _currentTrip_tripReport_Stops3;\n    _s();\n    const [events, setEvents] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [openEventModal, setOpenEventModal] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [currentEvent, setCurrentEvent] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [taskingEvents, setTaskingEvents] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    const [accordionValue, setAccordionValue] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const vesselID = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useSearchParams)().get(\"vesselID\") || \"0\";\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [edit_tripActivity, setEdit_tripActivity] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [displayRadioLogs, setDisplayRadioLogs] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [activityTypeOptions, setActivityTypeOptions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_32__.useBreakpoints)();\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_20__.hasPermission)(process.env.EDIT_LOGBOOKENTRY_ACTIVITY || \"EDIT_LOGBOOKENTRY_ACTIVITY\", permissions)) {\n                setEdit_tripActivity(true);\n            } else {\n                setEdit_tripActivity(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_20__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        init_permissions();\n        initData();\n    }, [\n        permissions\n    ]);\n    const initData = ()=>{\n        var _logBookConfig_customisedLogBookComponents;\n        const combinedFields = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents.nodes.filter((section)=>section.componentClass === \"SeaLogs\\\\EventType_LogBookComponent\" || section.componentClass === \"EventType_LogBookComponent\").reduce((acc, section)=>{\n            acc = acc.concat(section.customisedComponentFields.nodes);\n            return acc;\n        }, []);\n        const hasRescueType = combinedFields === null || combinedFields === void 0 ? void 0 : combinedFields.find((field)=>field.fieldName === \"VesselRescue\" || field.fieldName === \"HumanRescue\");\n        if (logBookConfig) {\n            const eventList = hasRescueType ? combinedFields.filter((field)=>!hasParent(field) && field.status !== \"Off\") : combinedFields === null || combinedFields === void 0 ? void 0 : combinedFields.filter((field)=>!hasParent(field) && field.status !== \"Off\" && field.fieldName !== \"TaskingStartUnderway\" && field.fieldName !== \"TaskingOnScene\" && field.fieldName !== \"TaskingOnTow\" && field.fieldName !== \"TaskingPaused\" && field.fieldName !== \"TaskingResumed\" && field.fieldName !== \"TaskingComplete\" && field.fieldName !== \"DangerousGoodsSailing\");\n            const filteredEvents = eventList === null || eventList === void 0 ? void 0 : eventList.map((event)=>({\n                    label: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_2__.getFieldName)(event).replace(/([a-z])([A-Z])/g, \"$1 $2\").replace(\"Passenger Arrival\", \"Arrival\").replace(\"Passenger Departure\", \"Departure\"),\n                    value: event.fieldName\n                })).filter((event, index, self)=>index === self.findIndex((e)=>e.value === event.value)).filter((event)=>// event?.value !== 'VesselRescue' &&\n                // event?.value !== 'HumanRescue' &&\n                // event?.value !== 'Supernumerary' &&\n                !isTowingField(event.value)).filter((event)=>checkVesselType(event.value));\n            // Add Incident Record as a custom activity type\n            // Incident Record is available for all vessel types\n            filteredEvents.push({\n                label: \"Incident Record\",\n                value: \"IncidentRecord\"\n            });\n            // Add Infringement Notices as a custom activity type if vessel type allows it\n            // InfringementNotices is only available for vessel types 0 and 1\n            const vesselTypeID = _app_lib_vesselTypes__WEBPACK_IMPORTED_MODULE_18__[\"default\"].findIndex((type)=>type == (vessel === null || vessel === void 0 ? void 0 : vessel.vesselType));\n            if ([\n                0,\n                1\n            ].includes(vesselTypeID)) {\n                filteredEvents.push({\n                    label: \"Infringement Notices\",\n                    value: \"InfringementNotice\"\n                });\n            }\n            if (permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_20__.hasPermission)(\"RECORD_TRAINING\", permissions)) {\n                setEvents(sortFilteredEvents(filteredEvents));\n            } else {\n                var _filteredEvents_filter;\n                setEvents(sortFilteredEvents((_filteredEvents_filter = filteredEvents === null || filteredEvents === void 0 ? void 0 : filteredEvents.filter((event)=>event.value !== \"CrewTraining\")) !== null && _filteredEvents_filter !== void 0 ? _filteredEvents_filter : []));\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        initData();\n    }, [\n        logBookConfig\n    ]);\n    const checkVesselType = (field)=>{\n        const vesselTypeID = _app_lib_vesselTypes__WEBPACK_IMPORTED_MODULE_18__[\"default\"].findIndex((type)=>type == (vessel === null || vessel === void 0 ? void 0 : vessel.vesselType));\n        const config = _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_19__.SLALL_LogBookFields.find((localField)=>localField.componentClass === \"EventType_LogBookComponent\");\n        const isVesselType = config === null || config === void 0 ? void 0 : config.items.find((localField)=>field === localField.value && localField.vesselType.includes(vesselTypeID));\n        return isVesselType ? true : false;\n    };\n    const sortFilteredEvents = (events)=>{\n        var _currentTrip_tripEvents_nodes, _currentTrip_tripEvents;\n        // Always ensure tasking events are available in the base events array\n        const taskingEvents = [\n            \"TaskingStartUnderway\",\n            \"TaskingOnScene\",\n            \"TaskingOnTow\",\n            \"TaskingComplete\",\n            \"TaskingPaused\",\n            \"TaskingResumed\"\n        ];\n        // Add missing tasking events to the events array\n        const eventsWithTasking = [\n            ...events\n        ];\n        taskingEvents.forEach((taskingType)=>{\n            if (!eventsWithTasking.find((event)=>event.value === taskingType)) {\n                eventsWithTasking.push({\n                    label: taskingType.replace(/([a-z])([A-Z])/g, \"$1 $2\"),\n                    value: taskingType\n                });\n            }\n        });\n        if (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes = _currentTrip_tripEvents.nodes) === null || _currentTrip_tripEvents_nodes === void 0 ? void 0 : _currentTrip_tripEvents_nodes.find((event)=>{\n            var _event_eventType_Tasking, _event_eventType_Tasking1;\n            return event.eventCategory === \"Tasking\" && ((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingStartUnderway\" && ((_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.status) === \"Open\";\n        })) {\n            var _currentTrip_tripEvents_nodes_filter, _currentTrip_tripEvents_nodes_filter1;\n            const openTask = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_filter = currentTrip.tripEvents.nodes.filter((event)=>{\n                var _event_eventType_Tasking;\n                return event.eventCategory === \"Tasking\" && ((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingStartUnderway\";\n            })) === null || _currentTrip_tripEvents_nodes_filter === void 0 ? void 0 : _currentTrip_tripEvents_nodes_filter.length;\n            const pausedTask = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_filter1 = currentTrip.tripEvents.nodes.filter((event)=>{\n                var _event_eventType_Tasking;\n                return event.eventCategory === \"Tasking\" && ((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingPaused\";\n            })) === null || _currentTrip_tripEvents_nodes_filter1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_filter1.length;\n            const sortedEvents = [\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingStartUnderway\" && openTask - pausedTask < 1).map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingOnScene\").map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingOnTow\").map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingComplete\").map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingPaused\").map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingResumed\" && pausedTask > 0).map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>!event.value.includes(\"Tasking\"))\n            ];\n            return sortedEvents;\n        }\n        return eventsWithTasking;\n    };\n    /*const colourStyles: StylesConfig = {\r\n        option: (\r\n            styles: any,\r\n            {\r\n                data,\r\n                isDisabled,\r\n                isFocused,\r\n                isSelected,\r\n            }: { data: any; isDisabled: any; isFocused: any; isSelected: any },\r\n        ) => {\r\n            const color = data.color\r\n            return {\r\n                ...styles,\r\n                backgroundColor: isDisabled\r\n                    ? undefined\r\n                    : isSelected\r\n                      ? data.bgColor\r\n                      : isFocused\r\n                        ? data.bgColor\r\n                        : data.bgColor + '60',\r\n                color: data.color,\r\n            }\r\n        },\r\n        singleValue: (styles: any, data: any) => ({\r\n            ...styles,\r\n            color: events.find((option: any) => option.value == data.data.value)\r\n                ?.color,\r\n        }),\r\n    }*/ const formatTime = (time)=>time.slice(0, 5);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        var _currentTrip_tripEvents_nodes_filter, _currentTrip_tripEvents;\n        const taskingEvents = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_filter = _currentTrip_tripEvents.nodes.filter((event)=>{\n            var _event_eventType_Tasking, _event_eventType_Tasking1, _event_eventType_Tasking2;\n            return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && ((event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingStartUnderway\" || (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.type) === \"TaskingPaused\") && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking2 = event.eventType_Tasking) === null || _event_eventType_Tasking2 === void 0 ? void 0 : _event_eventType_Tasking2.status) === \"Open\";\n        })) === null || _currentTrip_tripEvents_nodes_filter === void 0 ? void 0 : _currentTrip_tripEvents_nodes_filter.length;\n        setTaskingEvents(taskingEvents);\n    }, [\n        currentTrip\n    ]);\n    const hasParent = (field)=>{\n        const config = _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_19__.SLALL_LogBookFields.find((localField)=>localField.componentClass === \"EventType_LogBookComponent\");\n        const hasGroup = config === null || config === void 0 ? void 0 : config.items.find((localField)=>field.fieldName === localField.value && localField.groupTo);\n        return hasGroup ? true : false;\n    };\n    const isTowingField = (field)=>{\n        const config = _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_19__.SLALL_LogBookFields.find((localField)=>localField.componentClass === \"EventType_LogBookComponent\");\n        const isTowingCategory = config === null || config === void 0 ? void 0 : config.items.find((localField)=>field === localField.value && localField.type === \"TowingSubCategory\");\n        return isTowingCategory ? true : false;\n    };\n    const handleEventChange = (event)=>{\n        setCurrentEvent(false);\n        setCurrentStop(false);\n        setTripReport_Stops(false);\n        setDisplayDangerousGoodsPvpd(false);\n        setDisplayDangerousGoodsPvpdSailing(null);\n        fetchActivityTypes();\n        setCurrentEventType(event);\n    };\n    // const handleSetOpenEventModal = () => {\n    // setOpenEventModal(!openEventModal)\n    // }\n    const handleSetCurrentEventType = ()=>{\n        setCurrentEventType(false);\n        // Reset accordion state to properly close it\n        setAccordionValue(\"\");\n        setSelectedRow(0);\n        setCurrentEvent(false);\n        setCurrentStop(false);\n    // setOpenEventModal(false)\n    };\n    const previousDropEvent = (currentEvent)=>{\n        var _currentTrip_tripEvents;\n        const previousEvent = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventCategory) === \"PassengerDropFacility\" && (event === null || event === void 0 ? void 0 : event.id) !== (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id));\n        return previousEvent;\n    };\n    const mainTaskingEvent = (currentEvent)=>{\n        var _currentTrip_tripEvents;\n        const mainEvent = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.filter((event)=>{\n            var _event_eventType_Tasking;\n            return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : event.id) !== (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingStartUnderway\";\n        });\n        return mainEvent;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (events) {\n            var _currentTrip_tripEvents_nodes_filter, _currentTrip_tripEvents_nodes, _currentTrip_tripEvents;\n            let options = [];\n            if (taskingEvents === 0) {\n                options = events.filter((event)=>(event === null || event === void 0 ? void 0 : event.value) !== \"TaskingOnScene\" && (event === null || event === void 0 ? void 0 : event.value) !== \"TaskingOnTow\" && (event === null || event === void 0 ? void 0 : event.value) !== \"TaskingPaused\" && (event === null || event === void 0 ? void 0 : event.value) !== \"TaskingResumed\" && (event === null || event === void 0 ? void 0 : event.value) !== \"TaskingComplete\");\n                // Ensure TaskingStartUnderway is always available when no tasking events are open\n                const taskingStartUnderwayOption = events.find((event)=>(event === null || event === void 0 ? void 0 : event.value) === \"TaskingStartUnderway\");\n                if (taskingStartUnderwayOption && !options.find((option)=>option.value === \"TaskingStartUnderway\")) {\n                    options.push(taskingStartUnderwayOption);\n                } else if (!taskingStartUnderwayOption) {\n                    // If TaskingStartUnderway is not in the events array, create it manually\n                    options.push({\n                        label: \"Tasking Start Underway\",\n                        value: \"TaskingStartUnderway\"\n                    });\n                }\n            } else {\n                var _currentTrip_tripEvents1, _currentTrip_tripEvents2, _currentTrip_tripEvents3;\n                const taskingOpen = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : _currentTrip_tripEvents1.nodes.filter((event)=>{\n                    var _event_eventType_Tasking, _event_eventType_Tasking1;\n                    return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingStartUnderway\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.status) === \"Open\";\n                });\n                const taskingPaused = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents2 = currentTrip.tripEvents) === null || _currentTrip_tripEvents2 === void 0 ? void 0 : _currentTrip_tripEvents2.nodes.filter((event)=>{\n                    var _event_eventType_Tasking;\n                    return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingPaused\";\n                });\n                const taskingResumed = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents3 = currentTrip.tripEvents) === null || _currentTrip_tripEvents3 === void 0 ? void 0 : _currentTrip_tripEvents3.nodes.filter((event)=>{\n                    var _event_eventType_Tasking;\n                    return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingResumed\";\n                });\n                if ((taskingOpen === null || taskingOpen === void 0 ? void 0 : taskingOpen.length) > 0) {\n                    if ((taskingPaused === null || taskingPaused === void 0 ? void 0 : taskingPaused.length) === (taskingResumed === null || taskingResumed === void 0 ? void 0 : taskingResumed.length)) {\n                        options = events.filter((event)=>(event === null || event === void 0 ? void 0 : event.value) !== \"TaskingResumed\");\n                    }\n                    if ((taskingPaused === null || taskingPaused === void 0 ? void 0 : taskingPaused.length) > (taskingResumed === null || taskingResumed === void 0 ? void 0 : taskingResumed.length)) {\n                        options = events.filter((event)=>event.value !== \"TaskingOnScene\" && event.value !== \"TaskingOnTow\" && event.value !== \"TaskingPaused\");\n                    }\n                } else {\n                    options = events.filter((event)=>event.value !== \"TaskingOnScene\" && event.value !== \"TaskingOnTow\" && event.value !== \"TaskingPaused\" && event.value !== \"TaskingResumed\" && event.value !== \"TaskingComplete\");\n                    // Ensure TaskingStartUnderway is available when no open tasking events exist\n                    const taskingStartUnderwayOption = events.find((event)=>(event === null || event === void 0 ? void 0 : event.value) === \"TaskingStartUnderway\");\n                    if (taskingStartUnderwayOption && !options.find((option)=>option.value === \"TaskingStartUnderway\")) {\n                        options.push(taskingStartUnderwayOption);\n                    } else if (!taskingStartUnderwayOption) {\n                        // If TaskingStartUnderway is not in the events array, create it manually\n                        options.push({\n                            label: \"Tasking Start Underway\",\n                            value: \"TaskingStartUnderway\"\n                        });\n                    }\n                }\n            }\n            // When taskingPaused > 0, ensure TaskingResumed and TaskingStartUnderway are available\n            const taskingPausedCount = (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes = _currentTrip_tripEvents.nodes) === null || _currentTrip_tripEvents_nodes === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_filter = _currentTrip_tripEvents_nodes.filter((event)=>{\n                var _event_eventType_Tasking;\n                return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingPaused\";\n            })) === null || _currentTrip_tripEvents_nodes_filter === void 0 ? void 0 : _currentTrip_tripEvents_nodes_filter.length) || 0;\n            if (taskingPausedCount > 0) {\n                // Find TaskingResumed and TaskingStartUnderway from the original events array\n                const taskingResumedOption = events.find((event)=>(event === null || event === void 0 ? void 0 : event.value) === \"TaskingResumed\");\n                const taskingStartUnderwayOption = events.find((event)=>(event === null || event === void 0 ? void 0 : event.value) === \"TaskingStartUnderway\");\n                // Add TaskingResumed if it exists in events but not in current options\n                if (taskingResumedOption && !options.find((option)=>option.value === \"TaskingResumed\")) {\n                    options.push(taskingResumedOption);\n                }\n                // Add TaskingStartUnderway if it exists in events but not in current options\n                if (taskingStartUnderwayOption && !options.find((option)=>option.value === \"TaskingStartUnderway\")) {\n                    options.push(taskingStartUnderwayOption);\n                } else if (!taskingStartUnderwayOption) {\n                    // If TaskingStartUnderway is not in the events array, create it manually\n                    options.push({\n                        label: \"Tasking Start Underway\",\n                        value: \"TaskingStartUnderway\"\n                    });\n                }\n            }\n            options = options.map((option)=>{\n                if (option.value.includes(\"Tasking\") && option.value !== \"TaskingStartUnderway\" && !option.className) {\n                    return {\n                        ...option,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    };\n                }\n                return option;\n            });\n            // Remove duplicate by checking the options.value\n            options = options.filter((option, index, self)=>index === self.findIndex((o)=>o.value === option.value));\n            // Remove InfringementNotices from options because there's already InfringementNotice (without an 's').\n            options = options.filter((option)=>option.value !== \"InfringementNotices\");\n            // Remove HumanRescue and VesselRescue from options since it's already included in Tasking\n            options = options.filter((option)=>option.value !== \"HumanRescue\" && option.value !== \"VesselRescue\");\n            // Using lodash set the case options.label to only capitalize the first word\n            options = options.map((option)=>{\n                return {\n                    ...option,\n                    label: lodash_upperFirst__WEBPACK_IMPORTED_MODULE_16___default()(lodash_lowerCase__WEBPACK_IMPORTED_MODULE_14___default()(option.label))\n                };\n            });\n            // For options that starts with \"Tasking\", put it on top and change the order of these to be: Tasking start underway, Tasking on scene, Tasking on tow, Tasking paused, Tasking complete.\n            options = options.sort((a, b)=>{\n                const aIsTasking = a.value.startsWith(\"Tasking\");\n                const bIsTasking = b.value.startsWith(\"Tasking\");\n                // If both are tasking options, sort by predefined order\n                if (aIsTasking && bIsTasking) {\n                    const taskingOrder = [\n                        \"TaskingStartUnderway\",\n                        \"TaskingOnScene\",\n                        \"TaskingOnTow\",\n                        \"TaskingPaused\",\n                        \"TaskingResumed\",\n                        \"TaskingComplete\"\n                    ];\n                    const aIndex = taskingOrder.indexOf(a.value);\n                    const bIndex = taskingOrder.indexOf(b.value);\n                    // If both are in the predefined order, sort by index\n                    if (aIndex !== -1 && bIndex !== -1) {\n                        return aIndex - bIndex;\n                    }\n                    // If only one is in predefined order, prioritize it\n                    if (aIndex !== -1) return -1;\n                    if (bIndex !== -1) return 1;\n                    // If neither is in predefined order, maintain original order\n                    return 0;\n                }\n                // If only one is tasking, prioritize tasking options\n                if (aIsTasking && !bIsTasking) return -1;\n                if (!aIsTasking && bIsTasking) return 1;\n                // If neither is tasking, maintain original order\n                return 0;\n            });\n            setActivityTypeOptions(options);\n        }\n    }, [\n        events,\n        currentTrip,\n        taskingEvents\n    ]);\n    const fetchActivityTypes = ()=>{\n        initData();\n    };\n    // Memoized function to handle stop accordion item clicks\n    const handleStopAccordionItemClick = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((eventId)=>{\n        return ()=>{\n            // Toggle accordion state\n            if (accordionValue === \"stop_\".concat(eventId)) {\n                setAccordionValue(\"\");\n                setSelectedRow(0);\n                setCurrentEventType([]);\n                setCurrentEvent(false);\n                setCurrentStop(false);\n            } else {\n                var _currentTrip_tripReport_Stops;\n                setAccordionValue(\"stop_\".concat(eventId));\n                setSelectedRow(eventId);\n                setCurrentEventType({\n                    label: \"Passenger/vehicle pickup/drop off\",\n                    value: \"PassengerVehiclePickDrop\"\n                });\n                // Find the event by ID\n                const event = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripReport_Stops = currentTrip.tripReport_Stops) === null || _currentTrip_tripReport_Stops === void 0 ? void 0 : _currentTrip_tripReport_Stops.nodes.find((stop)=>stop.id === eventId);\n                setCurrentStop(event);\n                setDisplayDangerousGoodsPvpd(false);\n                setDisplayDangerousGoodsPvpdSailing(null);\n                setTripReport_Stops(false);\n            }\n        };\n    }, [\n        accordionValue,\n        currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripReport_Stops = currentTrip.tripReport_Stops) === null || _currentTrip_tripReport_Stops === void 0 ? void 0 : _currentTrip_tripReport_Stops.nodes,\n        setSelectedRow,\n        setCurrentEventType,\n        setCurrentEvent,\n        setCurrentStop,\n        setDisplayDangerousGoodsPvpd,\n        setDisplayDangerousGoodsPvpdSailing,\n        setTripReport_Stops\n    ]);\n    // Memoized function to generate stop display text\n    const getStopDisplayText = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((event)=>{\n        var _event_stopLocation, _event_stopLocation1;\n        return \"Passenger / Vehicle Pick & Drop - \".concat((event === null || event === void 0 ? void 0 : event.arriveTime) ? (event === null || event === void 0 ? void 0 : event.arriveTime) + \" (arr)\" : \"\", \" \").concat((event === null || event === void 0 ? void 0 : event.arriveTime) && (event === null || event === void 0 ? void 0 : event.departTime) ? \"-\" : \"\", \" \").concat((event === null || event === void 0 ? void 0 : event.departTime) ? (event === null || event === void 0 ? void 0 : event.departTime) + \" (dep)\" : \"\", \" \").concat((event === null || event === void 0 ? void 0 : (_event_stopLocation = event.stopLocation) === null || _event_stopLocation === void 0 ? void 0 : _event_stopLocation.title) ? event === null || event === void 0 ? void 0 : (_event_stopLocation1 = event.stopLocation) === null || _event_stopLocation1 === void 0 ? void 0 : _event_stopLocation1.title : \"\");\n    }, []);\n    const shouldIndent = (event)=>{\n        var _event_eventType_Tasking;\n        return event.eventCategory === \"Tasking\" && ((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) !== \"TaskingStartUnderway\";\n    };\n    const getEventLabel = (event)=>{\n        var _event_eventType_PassengerDropFacility_type_replace_replace, _event_eventType_PassengerDropFacility_type_replace, _event_eventType_PassengerDropFacility_type, _event_eventType_PassengerDropFacility, _event_eventType_Tasking;\n        return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"PassengerDropFacility\" ? event === null || event === void 0 ? void 0 : (_event_eventType_PassengerDropFacility = event.eventType_PassengerDropFacility) === null || _event_eventType_PassengerDropFacility === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type = _event_eventType_PassengerDropFacility.type) === null || _event_eventType_PassengerDropFacility_type === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type_replace = _event_eventType_PassengerDropFacility_type.replace(/([a-z])([A-Z])/g, \"$1 $2\")) === null || _event_eventType_PassengerDropFacility_type_replace === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type_replace_replace = _event_eventType_PassengerDropFacility_type_replace.replace(\"Passenger Arrival\", \"Arrival\")) === null || _event_eventType_PassengerDropFacility_type_replace_replace === void 0 ? void 0 : _event_eventType_PassengerDropFacility_type_replace_replace.replace(\"Passenger Departure\", \"Departure\") : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" ? event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type.replace(/([a-z])([A-Z])/g, \"$1 $2\") : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"EventSupernumerary\" ? \"Supernumerary\" : event === null || event === void 0 ? void 0 : event.eventCategory.replace(/([a-z])([A-Z])/g, \"$1 $2\");\n    };\n    const getEventValue = (event)=>{\n        var _event_eventType_PassengerDropFacility, _event_eventType_Tasking;\n        return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"PassengerDropFacility\" ? event === null || event === void 0 ? void 0 : (_event_eventType_PassengerDropFacility = event.eventType_PassengerDropFacility) === null || _event_eventType_PassengerDropFacility === void 0 ? void 0 : _event_eventType_PassengerDropFacility.type : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" ? event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Supernumerary\" ? \"EventSupernumerary\" : event === null || event === void 0 ? void 0 : event.eventCategory;\n    };\n    const getFuelTotals = (fuelLogs)=>{\n        const totalFuel = fuelLogs.reduce((acc, log)=>{\n            return acc + (log === null || log === void 0 ? void 0 : log.fuelAdded);\n        }, 0);\n        const totalCost = fuelLogs.reduce((acc, log)=>{\n            return acc + (log === null || log === void 0 ? void 0 : log.fuelAdded) * (log === null || log === void 0 ? void 0 : log.costPerLitre);\n        }, 0);\n        return \" - Total Fuel Added: \" + totalFuel + \"L, Total Cost: $\" + totalCost;\n    };\n    const getEventDisplayText = (event)=>{\n        var _eventType_geoLocation;\n        const category = event.eventCategory;\n        const eventType = event[\"eventType_\".concat(category)];\n        const geoLocation = eventType === null || eventType === void 0 ? void 0 : (_eventType_geoLocation = eventType.geoLocation) === null || _eventType_geoLocation === void 0 ? void 0 : _eventType_geoLocation.title;\n        const title = eventType === null || eventType === void 0 ? void 0 : eventType.title;\n        switch(category){\n            case \"PassengerDropFacility\":\n                var _eventType_type_replace_replace, _eventType_type_replace, _eventType_type;\n                return ((eventType === null || eventType === void 0 ? void 0 : eventType.time) ? eventType.time + \" - \" : \"\") + (eventType === null || eventType === void 0 ? void 0 : (_eventType_type = eventType.type) === null || _eventType_type === void 0 ? void 0 : (_eventType_type_replace = _eventType_type.replace(/([a-z])([A-Z])/g, \"$1 $2\")) === null || _eventType_type_replace === void 0 ? void 0 : (_eventType_type_replace_replace = _eventType_type_replace.replace(\"Passenger Arrival\", \"Arrival\")) === null || _eventType_type_replace_replace === void 0 ? void 0 : _eventType_type_replace_replace.replace(\"Passenger Departure\", \"Departure\")) + (title ? \" - \" + title : \"\") + (geoLocation ? \" - \" + geoLocation : \"\");\n            case \"Tasking\":\n                var _eventType_type1;\n                return (eventType === null || eventType === void 0 ? void 0 : eventType.time) + \" - \" + (eventType === null || eventType === void 0 ? void 0 : (_eventType_type1 = eventType.type) === null || _eventType_type1 === void 0 ? void 0 : _eventType_type1.replace(/([a-z])([A-Z])/g, \"$1 $2\")) + (title ? \" - \" + title : \"\") + (geoLocation ? \" - \" + geoLocation : \"\");\n            case \"BarCrossing\":\n                return ((eventType === null || eventType === void 0 ? void 0 : eventType.time) ? eventType.time + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (geoLocation ? \" - \" + geoLocation : \"\");\n            case \"RefuellingBunkering\":\n                return ((eventType === null || eventType === void 0 ? void 0 : eventType.date) ? dayjs__WEBPACK_IMPORTED_MODULE_23___default()(eventType.date).format(\"HH:mm\") + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (geoLocation ? \" - \" + geoLocation : \"\");\n            case \"RestrictedVisibility\":\n                var _eventType_startLocation;\n                return ((eventType === null || eventType === void 0 ? void 0 : eventType.crossingTime) ? eventType.crossingTime + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + ((eventType === null || eventType === void 0 ? void 0 : (_eventType_startLocation = eventType.startLocation) === null || _eventType_startLocation === void 0 ? void 0 : _eventType_startLocation.title) ? \" - \" + eventType.startLocation.title : \"\");\n            case \"TripUpdate\":\n                var _event_tripUpdate, _event_tripUpdate_geoLocation, _event_tripUpdate1, _event_tripUpdate_geoLocation1, _event_tripUpdate2;\n                return (((_event_tripUpdate = event.tripUpdate) === null || _event_tripUpdate === void 0 ? void 0 : _event_tripUpdate.date) ? dayjs__WEBPACK_IMPORTED_MODULE_23___default()(event.tripUpdate.date).format(\"HH:mm\") + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (((_event_tripUpdate1 = event.tripUpdate) === null || _event_tripUpdate1 === void 0 ? void 0 : (_event_tripUpdate_geoLocation = _event_tripUpdate1.geoLocation) === null || _event_tripUpdate_geoLocation === void 0 ? void 0 : _event_tripUpdate_geoLocation.title) ? \" - \" + ((_event_tripUpdate2 = event.tripUpdate) === null || _event_tripUpdate2 === void 0 ? void 0 : (_event_tripUpdate_geoLocation1 = _event_tripUpdate2.geoLocation) === null || _event_tripUpdate_geoLocation1 === void 0 ? void 0 : _event_tripUpdate_geoLocation1.title) : \"\");\n            case \"EventSupernumerary\":\n                var _event_supernumerary, _event_supernumerary1, _event_supernumerary2;\n                return ((event === null || event === void 0 ? void 0 : (_event_supernumerary = event.supernumerary) === null || _event_supernumerary === void 0 ? void 0 : _event_supernumerary.briefingTime) ? (event === null || event === void 0 ? void 0 : (_event_supernumerary1 = event.supernumerary) === null || _event_supernumerary1 === void 0 ? void 0 : _event_supernumerary1.briefingTime) + \" - \" : \"\") + \"Supernumerary\" + ((event === null || event === void 0 ? void 0 : (_event_supernumerary2 = event.supernumerary) === null || _event_supernumerary2 === void 0 ? void 0 : _event_supernumerary2.title) ? \" - \" + (event === null || event === void 0 ? void 0 : event.supernumerary.title) : \"\");\n            case \"IncidentRecord\":\n                var _event_incidentRecord, _event_incidentRecord1;\n                return (((_event_incidentRecord = event.incidentRecord) === null || _event_incidentRecord === void 0 ? void 0 : _event_incidentRecord.startDate) ? dayjs__WEBPACK_IMPORTED_MODULE_23___default()(event.incidentRecord.startDate).format(\"HH:mm\") + \" - \" : \"\") + \"Incident Record\" + (((_event_incidentRecord1 = event.incidentRecord) === null || _event_incidentRecord1 === void 0 ? void 0 : _event_incidentRecord1.title) ? \" - \" + event.incidentRecord.title : \"\");\n            case \"InfringementNotice\":\n                var _event_infringementNotice, _event_infringementNotice1, _event_infringementNotice2;\n                return ((event === null || event === void 0 ? void 0 : (_event_infringementNotice = event.infringementNotice) === null || _event_infringementNotice === void 0 ? void 0 : _event_infringementNotice.time) ? dayjs__WEBPACK_IMPORTED_MODULE_23___default()(\"\".concat(dayjs__WEBPACK_IMPORTED_MODULE_23___default()().format(\"YYYY-MM-DD\"), \" \").concat(event === null || event === void 0 ? void 0 : event.infringementNotice.time)).format(\"HH:mm\") + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + ((event === null || event === void 0 ? void 0 : (_event_infringementNotice1 = event.infringementNotice) === null || _event_infringementNotice1 === void 0 ? void 0 : _event_infringementNotice1.geoLocation.title) ? \" - \" + (event === null || event === void 0 ? void 0 : (_event_infringementNotice2 = event.infringementNotice) === null || _event_infringementNotice2 === void 0 ? void 0 : _event_infringementNotice2.geoLocation.title) : \"\");\n            case \"CrewTraining\":\n                var _event_crewTraining, _event_crewTraining_geoLocation, _event_crewTraining1, _event_crewTraining_geoLocation1, _event_crewTraining2;\n                return (((_event_crewTraining = event.crewTraining) === null || _event_crewTraining === void 0 ? void 0 : _event_crewTraining.startTime) ? event.crewTraining.startTime + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (((_event_crewTraining1 = event.crewTraining) === null || _event_crewTraining1 === void 0 ? void 0 : (_event_crewTraining_geoLocation = _event_crewTraining1.geoLocation) === null || _event_crewTraining_geoLocation === void 0 ? void 0 : _event_crewTraining_geoLocation.title) ? \" - \" + ((_event_crewTraining2 = event.crewTraining) === null || _event_crewTraining2 === void 0 ? void 0 : (_event_crewTraining_geoLocation1 = _event_crewTraining2.geoLocation) === null || _event_crewTraining_geoLocation1 === void 0 ? void 0 : _event_crewTraining_geoLocation1.title) : \"\");\n            case \"VesselRescue\":\n                var _event_eventType_VesselRescue_mission, _event_eventType_VesselRescue, _event_eventType_VesselRescue_mission1, _event_eventType_VesselRescue1, _event_eventType_VesselRescue2;\n                return (((_event_eventType_VesselRescue = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue === void 0 ? void 0 : (_event_eventType_VesselRescue_mission = _event_eventType_VesselRescue.mission) === null || _event_eventType_VesselRescue_mission === void 0 ? void 0 : _event_eventType_VesselRescue_mission.completedAt) ? ((_event_eventType_VesselRescue1 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue1 === void 0 ? void 0 : (_event_eventType_VesselRescue_mission1 = _event_eventType_VesselRescue1.mission) === null || _event_eventType_VesselRescue_mission1 === void 0 ? void 0 : _event_eventType_VesselRescue_mission1.completedAt) + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (((_event_eventType_VesselRescue2 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue2 === void 0 ? void 0 : _event_eventType_VesselRescue2.vesselName) ? \" - \" + event.eventType_VesselRescue.vesselName : \"\");\n            case \"HumanRescue\":\n                var _event_eventType_PersonRescue_mission, _event_eventType_PersonRescue, _event_eventType_PersonRescue_mission1, _event_eventType_PersonRescue1, _event_eventType_PersonRescue2;\n                return (((_event_eventType_PersonRescue = event.eventType_PersonRescue) === null || _event_eventType_PersonRescue === void 0 ? void 0 : (_event_eventType_PersonRescue_mission = _event_eventType_PersonRescue.mission) === null || _event_eventType_PersonRescue_mission === void 0 ? void 0 : _event_eventType_PersonRescue_mission.completedAt) ? ((_event_eventType_PersonRescue1 = event.eventType_PersonRescue) === null || _event_eventType_PersonRescue1 === void 0 ? void 0 : (_event_eventType_PersonRescue_mission1 = _event_eventType_PersonRescue1.mission) === null || _event_eventType_PersonRescue_mission1 === void 0 ? void 0 : _event_eventType_PersonRescue_mission1.completedAt) + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (((_event_eventType_PersonRescue2 = event.eventType_PersonRescue) === null || _event_eventType_PersonRescue2 === void 0 ? void 0 : _event_eventType_PersonRescue2.personName) ? \" - \" + event.eventType_PersonRescue.personName : \"\");\n            default:\n                return category.replace(/([a-z])([A-Z])/g, \"$1 $2\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row justify-between items-end\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_27__.H4, {\n                            children: \"ACTIVITIES\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 912,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_27__.P, {\n                            children: \"Record the events that happen during a voyage in this section.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 913,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                    lineNumber: 911,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                lineNumber: 910,\n                columnNumber: 13\n            }, this),\n            (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes = _currentTrip_tripEvents.nodes) === null || _currentTrip_tripEvents_nodes === void 0 ? void 0 : _currentTrip_tripEvents_nodes.length) > 0 || (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripReport_Stops1 = currentTrip.tripReport_Stops) === null || _currentTrip_tripReport_Stops1 === void 0 ? void 0 : (_currentTrip_tripReport_Stops_nodes = _currentTrip_tripReport_Stops1.nodes) === null || _currentTrip_tripReport_Stops_nodes === void 0 ? void 0 : _currentTrip_tripReport_Stops_nodes.length) > 0 || !currentEvent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: ((currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes1 = _currentTrip_tripEvents1.nodes) === null || _currentTrip_tripEvents_nodes1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes1.length) > 0 || (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripReport_Stops2 = currentTrip.tripReport_Stops) === null || _currentTrip_tripReport_Stops2 === void 0 ? void 0 : (_currentTrip_tripReport_Stops_nodes1 = _currentTrip_tripReport_Stops2.nodes) === null || _currentTrip_tripReport_Stops_nodes1 === void 0 ? void 0 : _currentTrip_tripReport_Stops_nodes1.length) > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_26__.Accordion, {\n                    type: \"single\",\n                    collapsible: true,\n                    value: accordionValue,\n                    onValueChange: (value)=>{\n                        setAccordionValue(value);\n                        // If we're closing the accordion, reset the state\n                        if (value === \"\") {\n                            setSelectedRow(0);\n                            // setOpenEventModal(false)\n                            setCurrentEventType([]);\n                            setCurrentEvent(false);\n                            setCurrentStop(false);\n                        }\n                    },\n                    children: [\n                        currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.tripEvents.nodes.map((event, index)=>{\n                            var _event_eventType_PassengerDropFacility_type_replace_replace, _event_eventType_PassengerDropFacility_type_replace, _event_eventType_PassengerDropFacility_type, _event_eventType_PassengerDropFacility, _event_eventType_Tasking, _event_eventType_PassengerDropFacility1, _event_eventType_Tasking1, _event_eventType_Tasking2, _event_eventType_Tasking3, _event_eventType_RefuellingBunkering_fuelLog_nodes, _event_eventType_RefuellingBunkering_fuelLog, _event_eventType_RefuellingBunkering, _event_eventType_RefuellingBunkering_fuelLog1, _event_eventType_RefuellingBunkering1, _event_eventType_Tasking4, _event_eventType_Tasking5, _event_eventType_Tasking6, _event_eventType_Tasking7;\n                            // Generate event label and value outside the JSX\n                            const eventLabel = (event === null || event === void 0 ? void 0 : event.eventCategory) === \"PassengerDropFacility\" ? event === null || event === void 0 ? void 0 : (_event_eventType_PassengerDropFacility = event.eventType_PassengerDropFacility) === null || _event_eventType_PassengerDropFacility === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type = _event_eventType_PassengerDropFacility.type) === null || _event_eventType_PassengerDropFacility_type === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type_replace = _event_eventType_PassengerDropFacility_type.replace(/([a-z])([A-Z])/g, \"$1 $2\")) === null || _event_eventType_PassengerDropFacility_type_replace === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type_replace_replace = _event_eventType_PassengerDropFacility_type_replace.replace(\"Passenger Arrival\", \"Arrival\")) === null || _event_eventType_PassengerDropFacility_type_replace_replace === void 0 ? void 0 : _event_eventType_PassengerDropFacility_type_replace_replace.replace(\"Passenger Departure\", \"Departure\") : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" ? event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type.replace(/([a-z])([A-Z])/g, \"$1 $2\") : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"EventSupernumerary\" ? \"Supernumerary\" : event === null || event === void 0 ? void 0 : event.eventCategory.replace(/([a-z])([A-Z])/g, \"$1 $2\");\n                            const eventValue = (event === null || event === void 0 ? void 0 : event.eventCategory) === \"PassengerDropFacility\" ? event === null || event === void 0 ? void 0 : (_event_eventType_PassengerDropFacility1 = event.eventType_PassengerDropFacility) === null || _event_eventType_PassengerDropFacility1 === void 0 ? void 0 : _event_eventType_PassengerDropFacility1.type : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" ? event === null || event === void 0 ? void 0 : (_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.type : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Supernumerary\" ? \"EventSupernumerary\" : event === null || event === void 0 ? void 0 : event.eventCategory;\n                            // Generate event display text\n                            const eventDisplayText = getEventDisplayText(event);\n                            // Handle click on accordion item\n                            const handleAccordionItemClick = ()=>{\n                                // Toggle accordion state\n                                if (accordionValue === event.id.toString()) {\n                                    setAccordionValue(\"\");\n                                    setSelectedRow(0);\n                                    // setOpenEventModal(false)\n                                    setCurrentEventType([]);\n                                    setCurrentEvent(false);\n                                    setCurrentStop(false);\n                                } else {\n                                    setAccordionValue(event.id.toString());\n                                    setSelectedRow(event.id);\n                                    // setOpenEventModal(true)\n                                    setCurrentEventType({\n                                        label: eventLabel,\n                                        value: eventValue\n                                    });\n                                    setCurrentEvent(event);\n                                    setTripReport_Stops(false);\n                                    setDisplayDangerousGoodsPvpd(false);\n                                    setDisplayDangerousGoodsPvpdSailing(null);\n                                }\n                            };\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_26__.AccordionItem, {\n                                value: event.id.toString(),\n                                className: (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking2 = event.eventType_Tasking) === null || _event_eventType_Tasking2 === void 0 ? void 0 : _event_eventType_Tasking2.type) !== \"TaskingStartUnderway\" ? \"ml-[1.5rem]\" : \"\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_26__.AccordionTrigger, {\n                                        onClick: handleAccordionItemClick,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center relative justify-between w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col inset-y-0 items-center justify-center absolute -left-[41px] sm:-left-[46px] w-5\",\n                                                    children: ((event === null || event === void 0 ? void 0 : event.eventCategory) !== \"Tasking\" || (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking3 = event.eventType_Tasking) === null || _event_eventType_Tasking3 === void 0 ? void 0 : _event_eventType_Tasking3.type) == \"TaskingStartUnderway\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_31__.cn)(\"size-[11px] z-10 rounded-full\", currentEvent.id === event.id ? \"border border-primary bg-curious-blue-200\" : currentEvent.eventCategory === event.eventCategory ? \"border border-primary bg-curious-blue-200\" : \"border border-cool-wedgewood-200 bg-outer-space-50\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                        lineNumber: 1048,\n                                                        columnNumber: 61\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1041,\n                                                    columnNumber: 53\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        eventDisplayText,\n                                                        (event === null || event === void 0 ? void 0 : event.eventCategory) === \"RefuellingBunkering\" && (event === null || event === void 0 ? void 0 : (_event_eventType_RefuellingBunkering = event.eventType_RefuellingBunkering) === null || _event_eventType_RefuellingBunkering === void 0 ? void 0 : (_event_eventType_RefuellingBunkering_fuelLog = _event_eventType_RefuellingBunkering.fuelLog) === null || _event_eventType_RefuellingBunkering_fuelLog === void 0 ? void 0 : (_event_eventType_RefuellingBunkering_fuelLog_nodes = _event_eventType_RefuellingBunkering_fuelLog.nodes) === null || _event_eventType_RefuellingBunkering_fuelLog_nodes === void 0 ? void 0 : _event_eventType_RefuellingBunkering_fuelLog_nodes.length) > 0 && getFuelTotals(event === null || event === void 0 ? void 0 : (_event_eventType_RefuellingBunkering1 = event.eventType_RefuellingBunkering) === null || _event_eventType_RefuellingBunkering1 === void 0 ? void 0 : (_event_eventType_RefuellingBunkering_fuelLog1 = _event_eventType_RefuellingBunkering1.fuelLog) === null || _event_eventType_RefuellingBunkering_fuelLog1 === void 0 ? void 0 : _event_eventType_RefuellingBunkering_fuelLog1.nodes)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1062,\n                                                    columnNumber: 53\n                                                }, this),\n                                                (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking4 = event.eventType_Tasking) === null || _event_eventType_Tasking4 === void 0 ? void 0 : _event_eventType_Tasking4.type) === \"TaskingStartUnderway\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking5 = event.eventType_Tasking) === null || _event_eventType_Tasking5 === void 0 ? void 0 : _event_eventType_Tasking5.status) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"\".concat((event === null || event === void 0 ? void 0 : (_event_eventType_Tasking6 = event.eventType_Tasking) === null || _event_eventType_Tasking6 === void 0 ? void 0 : _event_eventType_Tasking6.status) === \"Open\" ? \"text-bright-turquoise-600\" : \"\", \" pr-2\"),\n                                                    children: event === null || event === void 0 ? void 0 : (_event_eventType_Tasking7 = event.eventType_Tasking) === null || _event_eventType_Tasking7 === void 0 ? void 0 : _event_eventType_Tasking7.status\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1084,\n                                                    columnNumber: 61\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                            lineNumber: 1040,\n                                            columnNumber: 49\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                        lineNumber: 1036,\n                                        columnNumber: 45\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_26__.AccordionContent, {\n                                        className: \"pb-4\",\n                                        children: currentEventType && currentEvent && currentEvent.id === event.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                currentEventType.value === \"VesselRescue\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_vessel_rescue__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    offline: offline,\n                                                    geoLocations: geoLocations,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    locked: locked || !edit_tripActivity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1103,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"HumanRescue\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_person_rescue__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    offline: offline,\n                                                    geoLocations: geoLocations,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    locked: locked || !edit_tripActivity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1133,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"RestrictedVisibility\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_restricted_visibility__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    logBookConfig: logBookConfig,\n                                                    locked: locked || !edit_tripActivity,\n                                                    members: crewMembers\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1163,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"BarCrossing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_bar_crossing__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    members: crewMembers,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    logBookConfig: logBookConfig,\n                                                    locked: locked || !edit_tripActivity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1196,\n                                                    columnNumber: 65\n                                                }, this),\n                                                (currentEventType.value === \"PassengerArrival\" || currentEventType.value === \"PassengerDeparture\" || currentEventType.value === \"WaterTaxiService\" || currentEventType.value === \"ScheduledPassengerService\") && //TODO: update this form\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_passenger_drop_facility__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    offline: offline,\n                                                    geoLocations: geoLocations,\n                                                    inLogbook: true,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    type: currentEventType.value,\n                                                    logBookConfig: logBookConfig,\n                                                    previousDropEvent: previousDropEvent(currentEvent),\n                                                    vessel: vessel,\n                                                    locked: locked || !edit_tripActivity,\n                                                    fuelLogs: fuelLogs\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1236,\n                                                    columnNumber: 65\n                                                }, this),\n                                                (currentEventType.value === \"TaskingStartUnderway\" || currentEventType.value === \"TaskingOnScene\" || currentEventType.value === \"TaskingOnTow\" || currentEventType.value === \"TaskingPaused\" || currentEventType.value === \"TaskingResumed\" || currentEventType.value === \"TaskingComplete\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_tasking__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    offline: offline,\n                                                    geoLocations: geoLocations,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    type: currentEventType.value,\n                                                    logBookConfig: logBookConfig,\n                                                    previousDropEvent: mainTaskingEvent(currentEvent),\n                                                    vessel: vessel,\n                                                    members: crewMembers,\n                                                    locked: locked || !edit_tripActivity,\n                                                    fuelLogs: fuelLogs\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1292,\n                                                    columnNumber: 65\n                                                }, this),\n                                                permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_20__.hasPermission)(\"RECORD_TRAINING\", permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: currentEventType.value === \"CrewTraining\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_crew_training_event__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        offline: offline,\n                                                        vesselId: +vesselID,\n                                                        trainingTypeId: 0,\n                                                        currentTrip: currentTrip,\n                                                        updateTripReport: updateTripReport,\n                                                        selectedEvent: currentEvent,\n                                                        tripReport: tripReport,\n                                                        closeModal: handleSetCurrentEventType,\n                                                        crewMembers: crewMembers,\n                                                        masterID: masterID,\n                                                        logBookConfig: logBookConfig,\n                                                        vessels: vessels,\n                                                        locked: locked || !edit_tripActivity,\n                                                        logBookStartDate: logBookStartDate\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                        lineNumber: 1346,\n                                                        columnNumber: 77\n                                                    }, this)\n                                                }, void 0, false),\n                                                currentEventType.value === \"EventSupernumerary\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_supernumerary_event__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    offline: offline,\n                                                    logBookConfig: logBookConfig,\n                                                    locked: locked || !edit_tripActivity,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    tripReport: tripReport,\n                                                    selectedEvent: currentEvent\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1396,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"RefuellingBunkering\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_refuelling_bunkering__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    logBookConfig: logBookConfig,\n                                                    locked: locked || !edit_tripActivity,\n                                                    mainFuelLogs: fuelLogs\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1426,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"TripUpdate\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_trip_update__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    locked: locked || !edit_tripActivity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1462,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"PilotTransfer\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_pilot_transfer__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    locked: locked || !edit_tripActivity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1489,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"InfringementNotice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_InfringementNotices__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    crewMembers: crewMembers,\n                                                    locked: locked || !edit_tripActivity,\n                                                    visibility: // selectedRow ===\n                                                    //     event.id &&\n                                                    currentEventType && currentEvent\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1516,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"IncidentRecord\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_incident_record_incident_record_form__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    id: (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.incidentRecordID) || 0,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    inLogbook: true,\n                                                    selectedEvent: currentEvent,\n                                                    offline: offline,\n                                                    tripReport: tripReport,\n                                                    logBookStartDate: logBookStartDate\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1552,\n                                                    columnNumber: 65\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                        lineNumber: 1095,\n                                        columnNumber: 45\n                                    }, this)\n                                ]\n                            }, index + \"_events\", true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                lineNumber: 1024,\n                                columnNumber: 41\n                            }, this);\n                        }),\n                        currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripReport_Stops3 = currentTrip.tripReport_Stops) === null || _currentTrip_tripReport_Stops3 === void 0 ? void 0 : _currentTrip_tripReport_Stops3.nodes.map((event, index)=>{\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_26__.AccordionItem, {\n                                value: \"stop_\".concat(event.id),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_26__.AccordionTrigger, {\n                                        onClick: handleStopAccordionItemClick(event.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: getStopDisplayText(event)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                lineNumber: 1601,\n                                                columnNumber: 53\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                            lineNumber: 1600,\n                                            columnNumber: 49\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                        lineNumber: 1596,\n                                        columnNumber: 45\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_26__.AccordionContent, {\n                                        className: \"pb-4\",\n                                        children: currentEventType && currentStop && currentStop.id === event.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: currentEventType.value === \"PassengerVehiclePickDrop\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_passenger_vehicle_pick_drop_passenger_vehicle_pick_drop__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                offline: offline,\n                                                geoLocations: geoLocations,\n                                                updateTripReport: updateTripReport,\n                                                currentTrip: currentTrip,\n                                                selectedEvent: currentStop,\n                                                tripReport: tripReport,\n                                                closeModal: handleSetCurrentEventType,\n                                                type: currentEventType.value,\n                                                logBookConfig: logBookConfig,\n                                                members: crewMembers,\n                                                locked: locked || !edit_tripActivity,\n                                                tripReport_Stops: tripReport_Stops,\n                                                setTripReport_Stops: setTripReport_Stops,\n                                                displayDangerousGoods: displayDangerousGoodsPvpd,\n                                                setDisplayDangerousGoods: setDisplayDangerousGoodsPvpd,\n                                                displayDangerousGoodsSailing: displayDangerousGoodsPvpdSailing,\n                                                setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsPvpdSailing,\n                                                allPVPDDangerousGoods: allPVPDDangerousGoods,\n                                                setAllPVPDDangerousGoods: setAllPVPDDangerousGoods,\n                                                selectedDGR: selectedDGRPVPD,\n                                                setSelectedDGR: setSelectedDGRPVPD\n                                            }, \"pvpd-\".concat(event.id), false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                lineNumber: 1616,\n                                                columnNumber: 65\n                                            }, this)\n                                        }, void 0, false)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                        lineNumber: 1608,\n                                        columnNumber: 45\n                                    }, this)\n                                ]\n                            }, index + \"_stops\", true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                lineNumber: 1593,\n                                columnNumber: 41\n                            }, this);\n                        })\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                    lineNumber: 926,\n                    columnNumber: 25\n                }, this)\n            }, void 0, false) : null,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-start gap-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_25__.Label, {\n                    position: (0,_utils_responsiveLabel__WEBPACK_IMPORTED_MODULE_33__.getResponsiveLabel)(bp.phablet, \"top\", \"left\"),\n                    className: \"w-full\",\n                    label: \"Activity Type \",\n                    children: activityTypeOptions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_24__.Combobox, {\n                        id: \"task-assigned\",\n                        options: activityTypeOptions,\n                        value: currentEventType,\n                        onChange: handleEventChange,\n                        title: \"Activity type\",\n                        placeholder: \"Activity type\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                        lineNumber: 1704,\n                        columnNumber: 25\n                    }, this) : // Failsafe - in case the activity types are not loaded.\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            variant: \"outline\",\n                            onClick: fetchActivityTypes,\n                            children: \"Refresh activity types\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1715,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                        lineNumber: 1714,\n                        columnNumber: 25\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                    lineNumber: 1697,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                lineNumber: 1695,\n                columnNumber: 13\n            }, this),\n            currentEventType && !currentEvent && !currentStop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    currentEventType.value === \"VesselRescue\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_vessel_rescue__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            offline: offline,\n                            geoLocations: geoLocations,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            locked: locked || !edit_tripActivity\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1749,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"HumanRescue\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_person_rescue__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            offline: offline,\n                            geoLocations: geoLocations,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            locked: locked || !edit_tripActivity\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1763,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"RestrictedVisibility\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_restricted_visibility__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            offline: offline,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            logBookConfig: logBookConfig,\n                            locked: locked || !edit_tripActivity,\n                            members: crewMembers\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1777,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"BarCrossing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_bar_crossing__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            offline: offline,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            members: crewMembers,\n                            closeModal: handleSetCurrentEventType,\n                            logBookConfig: logBookConfig,\n                            locked: locked || !edit_tripActivity\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1792,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    (currentEventType.value === \"PassengerArrival\" || currentEventType.value === \"PassengerDeparture\" || currentEventType.value === \"WaterTaxiService\" || currentEventType.value === \"ScheduledPassengerService\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_passenger_drop_facility__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            offline: offline,\n                            geoLocations: geoLocations,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            type: currentEventType.value,\n                            logBookConfig: logBookConfig,\n                            inLogbook: true,\n                            previousDropEvent: previousDropEvent(currentEvent),\n                            vessel: vessel,\n                            locked: locked || !edit_tripActivity,\n                            fuelLogs: fuelLogs\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1811,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    (currentEventType.value === \"TaskingStartUnderway\" || currentEventType.value === \"TaskingOnScene\" || currentEventType.value === \"TaskingOnTow\" || currentEventType.value === \"TaskingPaused\" || currentEventType.value === \"TaskingResumed\" || currentEventType.value === \"TaskingComplete\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_tasking__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            inLogbook: true,\n                            offline: offline,\n                            geoLocations: geoLocations,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            type: currentEventType.value,\n                            logBookConfig: logBookConfig,\n                            previousDropEvent: mainTaskingEvent(currentEvent),\n                            vessel: vessel,\n                            members: crewMembers,\n                            locked: locked || !edit_tripActivity,\n                            fuelLogs: fuelLogs\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1838,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_20__.hasPermission)(\"RECORD_TRAINING\", permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: currentEventType.value === \"CrewTraining\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_crew_training_event__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                offline: offline,\n                                vesselId: +vesselID,\n                                trainingTypeId: 0,\n                                currentTrip: currentTrip,\n                                updateTripReport: updateTripReport,\n                                selectedEvent: currentEvent,\n                                tripReport: tripReport,\n                                closeModal: handleSetCurrentEventType,\n                                crewMembers: crewMembers,\n                                masterID: masterID,\n                                logBookConfig: logBookConfig,\n                                vessels: vessels,\n                                locked: locked || !edit_tripActivity,\n                                logBookStartDate: logBookStartDate\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                lineNumber: 1864,\n                                columnNumber: 41\n                            }, this)\n                        }, void 0, false)\n                    }, void 0, false),\n                    currentEventType.value === \"EventSupernumerary\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_supernumerary_event__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            inLogbook: true,\n                            offline: offline,\n                            logBookConfig: logBookConfig,\n                            locked: locked || !edit_tripActivity,\n                            closeModal: handleSetCurrentEventType,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            tripReport: tripReport,\n                            selectedEvent: currentEvent\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1890,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"PassengerVehiclePickDrop\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_passenger_vehicle_pick_drop_passenger_vehicle_pick_drop__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            offline: offline,\n                            geoLocations: geoLocations,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentStop,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            type: currentEventType.value,\n                            logBookConfig: logBookConfig,\n                            members: crewMembers,\n                            locked: locked || !edit_tripActivity,\n                            tripReport_Stops: tripReport_Stops,\n                            setTripReport_Stops: setTripReport_Stops,\n                            displayDangerousGoods: displayDangerousGoodsPvpd,\n                            setDisplayDangerousGoods: setDisplayDangerousGoodsPvpd,\n                            displayDangerousGoodsSailing: displayDangerousGoodsPvpdSailing,\n                            setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsPvpdSailing,\n                            allPVPDDangerousGoods: allPVPDDangerousGoods,\n                            setAllPVPDDangerousGoods: setAllPVPDDangerousGoods,\n                            selectedDGR: selectedDGRPVPD,\n                            setSelectedDGR: setSelectedDGRPVPD\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1905,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"RefuellingBunkering\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_refuelling_bunkering__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            offline: offline,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            logBookConfig: logBookConfig,\n                            locked: locked || !edit_tripActivity,\n                            mainFuelLogs: fuelLogs\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1942,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"TripUpdate\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_trip_update__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            offline: offline,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            locked: locked || !edit_tripActivity\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1957,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"InfringementNotice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_InfringementNotices__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            offline: offline,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            crewMembers: crewMembers,\n                            locked: locked || !edit_tripActivity,\n                            visibility: currentEventType && !currentEvent && !currentStop\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1984,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"IncidentRecord\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_incident_record_incident_record_form__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                            id: (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.incidentRecordID) || 0,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            closeModal: handleSetCurrentEventType,\n                            inLogbook: true,\n                            selectedEvent: currentEvent,\n                            offline: offline,\n                            tripReport: tripReport,\n                            logBookStartDate: logBookStartDate\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 2003,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false)\n                ]\n            }, void 0, true),\n            currentTrip.tripReportScheduleID > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radio_logs_schedule__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                open: displayRadioLogs,\n                setOpen: setDisplayRadioLogs,\n                currentTrip: currentTrip\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                lineNumber: 2019,\n                columnNumber: 17\n            }, this)\n        ]\n    }, lodash_uniqueId__WEBPACK_IMPORTED_MODULE_15___default()(), true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n        lineNumber: 909,\n        columnNumber: 9\n    }, this);\n}\n_s(Events, \"kBeEJRvj+liqmcuZ7jwYORdkupw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useSearchParams,\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_32__.useBreakpoints\n    ];\n});\n_c = Events;\nvar _c;\n$RefreshReg$(_c, \"Events\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/events.tsx\n"));

/***/ })

});